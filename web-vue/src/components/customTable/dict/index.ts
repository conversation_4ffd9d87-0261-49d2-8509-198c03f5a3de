///
/// Copyright (c) 2019 Of Him Code Technology Studio
/// Jpom is licensed under Mulan PSL v2.
/// You can use this software according to the terms and conditions of the Mulan PSL v2.
/// You may obtain a copy of Mulan PSL v2 at:
/// 			http://license.coscl.org.cn/MulanPSL2
/// THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
/// See the Mulan PSL v2 for more details.
///

import { t } from '@/i18n'
export const tableSizeList = [
  {
    value: 'large',
    label: t('i18n_949934d97c')
  },
  {
    value: 'middle',
    label: t('i18n_eadd05ba6a')
  },
  {
    value: 'small',
    label: t('i18n_03e59bb33c')
  }
]

export const tableLayoutList = [
  {
    value: 'table',
    label: t('i18n_b339aa8710')
  },
  {
    value: 'card',
    label: t('i18n_d87f215d9a')
  }
]
