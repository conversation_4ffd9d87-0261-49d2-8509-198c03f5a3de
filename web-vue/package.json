{"name": "jpom-vue3", "private": true, "version": "2.11.12", "type": "module", "scripts": {"dev": "vite --mode dev", "local": "vite --mode loc", "build": "vite build", "lint": "eslint --ext .ts,.js,.jsx,.vue .", "lint:fix": "eslint --fix --ext .ts,.js,.jsx,.vue .", "preview": "vite preview", "i18n": "node ./bin/i18n.cjs"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@codemirror/lang-javascript": "^6.2.2", "ant-design-vue": "^4.1.2", "axios": "^1.6.8", "base64-js": "^1.5.1", "codemirror": "^5.65.16", "codemirror-editor-vue3": "^2.4.1", "dayjs": "^1.11.10", "echarts": "^5.5.0", "js-sha1": "^0.7.0", "markdown-it": "^14.1.0", "pinia": "^2.1.7", "prismjs": "^1.29.0", "qs": "^6.12.1", "spark-md5": "^3.0.2", "vue": "^3.4.21", "vue-i18n": "^9.12.1", "vue-router": "^4.3.0", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-smooth-dnd": "^0.0.6", "xterm": "^5.3.0", "xterm-addon-attach": "^0.9.0", "xterm-addon-fit": "^0.8.0"}, "devDependencies": {"@types/node": "^20.10.8", "@types/vue-i18n": "^7.0.0", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "dotenv": "^16.4.5", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.25.0", "https": "^1.0.0", "jpom-i18n": "^1.0.17", "less": "^4.2.0", "prettier": "^3.2.5", "rollup-plugin-visualizer": "^5.12.0", "typescript": "^5.4.5", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.2.8", "vite-plugin-html": "^3.2.2"}}