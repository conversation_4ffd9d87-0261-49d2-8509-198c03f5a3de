#
# Copyright (c) 2019 Of Him Code Technology Studio
# Jpom is licensed under Mulan PSL v2.
# You can use this software according to the terms and conditions of the Mulan PSL v2.
# You may obtain a copy of Mulan PSL v2 at:
# 			http://license.coscl.org.cn/MulanPSL2
# THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
# See the Mulan PSL v2 for more details.
#

version: '3.8'
services:
  server:
    env_file:
      - env-release.env
    image: jpomdocker/jpom:server-${JPOM_VERSION}
    build:
      dockerfile: ./modules/server/Dockerfile
      context: .
      args:
        JPOM_VERSION: ${JPOM_VERSION}
    environment:
      "JPOM_SERVER_TEMP_TOKEN": ${SERVER_TOKEN}
    volumes:
      - jpom-server-data:/usr/local/jpom-server/data
      - jpom-server-logs:/usr/local/jpom-server/logs
      - jpom-server-conf:/usr/local/jpom-server/conf
    ports:
      - "2122:2122"
    hostname: server
  agent01:
    env_file:
      - env-release.env
    image: jpomdocker/jpom:agent-${JPOM_VERSION}
    build:
      dockerfile: ./modules/agent/Dockerfile
      context: .
      args:
        JPOM_VERSION: ${JPOM_VERSION}
        RUN_ARG: --auto-push-to-server 'http://server:2122/api/node/receive_push?token=${SERVER_TOKEN}&workspaceId=DEFAULT'

    volumes:
      - jpom-agent01:/usr/local/jpom-agent
    ports:
      - "2123:2123"
    depends_on:
      - server
    hostname: agent01
  agent02:
    env_file:
      - env-release.env
    image: jpomdocker/jpom:agent-${JPOM_VERSION}
    build:
      dockerfile: ./modules/agent/Dockerfile
      context: .
      args:
        JPOM_VERSION: ${JPOM_VERSION}
        RUN_ARG: --auto-push-to-server 'http://server:2122/api/node/receive_push?token=${SERVER_TOKEN}&workspaceId=DEFAULT'

    volumes:
      - jpom-agent02:/usr/local/jpom-agent
    ports:
      - "2124:2123"
    depends_on:
      - server
    hostname: agent02
volumes:
  jpom-agent01:
  jpom-agent02:
  jpom-server-data:
  jpom-server-logs:
  jpom-server-conf:
