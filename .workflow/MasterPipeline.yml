#
# Copyright (c) 2019 Of Him Code Technology Studio
# Jpom is licensed under Mulan PSL v2.
# You can use this software according to the terms and conditions of the Mulan PSL v2.
# You may obtain a copy of Mulan PSL v2 at:
# 			http://license.coscl.org.cn/MulanPSL2
# THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
# See the Mulan PSL v2 for more details.
#

version: '1.0'
name: master-pipeline
displayName: MasterPipeline
triggers:
  trigger: manual
  push:
    branches:
      precise:
        - master
        - dev
stages:
  - name: compile
    displayName: 编译
    strategy: naturally
    trigger: auto
    steps:
      - step: build@maven
        name: build_maven
        displayName: Maven 构建
        jdkVersion: 8
        mavenVersion: 3.6.3
        commands:
          - curl -LfsSo /opt/node-v18.19.0-linux-x64.tar.gz https://npmmirror.com/mirrors/node/v18.19.0/node-v18.19.0-linux-x64.tar.gz
          - tar -zxf /opt/node-v18.19.0-linux-x64.tar.gz -C /opt/ && export PATH=/opt/node-v18.19.0-linux-x64/bin:$PATH
          - npm config set registry https://registry.npmmirror.com/
          - cd web-vue && npm install && npm run build
          - cd ..
          - mvn -B -e clean package -Dmaven.test.skip=true -Dmaven.compile.fork=true -s script/settings.xml
        artifacts:
          - name: all_zip
            path:
              - modules/server/target/server-2.11.12-release.zip
              - modules/agent/target/agent-2.11.12-release.zip
          - name: server_zip
            path:
              - modules/server/target/server-2.11.12-release.zip
          - name: agent_zip
            path:
              - modules/agent/target/agent-2.11.12-release.zip
        settings: []
        strategy:
          retry: '0'
  - name: stage-0aa9dee2
    displayName: 打包
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: publish@general_artifacts
        name: publish_general_artifacts
        displayName: 合并打包
        dependArtifact: all_zip
        artifactName: jpom-2.11.12
        strategy:
          retry: '0'
strategy:
  blocking: true
permissions:
  - role: admin
    members: []
